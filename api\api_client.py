import requests
import base64
from io import BytesIO
from PIL import Image
import json
from config_manager import ConfigManager
from logger import get_logger

class TogetherAIClient:
    """Client for interacting with Together AI API for image generation."""

    BASE_URL = "https://api.together.xyz/v1/images/generations"

    def __init__(self, api_key=None, config_manager=None):
        """Initialize the Together AI client.

        Args:
            api_key (str, optional): Together AI API key. If not provided,
                                     will look for key in config manager.
            config_manager (ConfigManager, optional): Config manager instance.
        """
        self.logger = get_logger()
        self.config_manager = config_manager or ConfigManager()

        # Get API key from parameter or config
        self.api_key = api_key
        if not self.api_key:
            self.api_key = self.config_manager.get_api_key("together_ai")
            self.logger.debug(f"Using API key from config: {'*' * 5}{self.api_key[-5:] if self.api_key else 'None'}")
        else:
            self.logger.debug(f"Using provided API key: {'*' * 5}{self.api_key[-5:] if self.api_key else 'None'}")

        if not self.api_key:
            self.logger.error("No API key found in config or provided as parameter")
            raise ValueError("API key must be provided or set in the config file")

    def generate_image(self, prompt, width=1024, height=768, steps=4, seed=None, model_id=None):
        """Generate an image using the Together AI API.

        Args:
            prompt (str): Text prompt for image generation
            width (int, optional): Width of the generated image. Defaults to 1024.
            height (int, optional): Height of the generated image. Defaults to 768.
            steps (int, optional): Number of generation steps. Defaults to 4.
            seed (int, optional): Seed for reproducible generation. Defaults to None.
            model_id (str, optional): Model ID to use. If None, uses the first available model.

        Returns:
            PIL.Image.Image: Generated image as PIL Image object

        Raises:
            Exception: If the API request fails
        """
        self.logger.debug(f"Generating image with prompt: '{prompt}', size: {width}x{height}, steps: {steps}, seed: {seed}, model: {model_id}")

        # Ensure width and height are multiples of 64 for Together AI
        width = int(width)
        height = int(height)
        adjusted_width = ((width + 32) // 64) * 64
        adjusted_height = ((height + 32) // 64) * 64

        # Ensure dimensions are within the maximum allowed (1408)
        adjusted_width = min(adjusted_width, 1408)
        adjusted_height = min(adjusted_height, 1408)

        if adjusted_width != width or adjusted_height != height:
            self.logger.debug(f"Adjusted dimensions from {width}x{height} to {adjusted_width}x{adjusted_height} for Together AI")
            width = adjusted_width
            height = adjusted_height

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Together AI only has one free model, so we always use the default
        # model_id parameter is ignored for compatibility with Runware AI interface
        model_id = self.get_available_models()[0]["id"]
        self.logger.debug(f"Together AI only has one free model, using: {model_id}")

        payload = {
            "model": model_id,  # Use the model ID from get_available_models
            "prompt": prompt,
            "width": width,
            "height": height,
            "steps": steps,
            "response_format": "b64_json"
        }

        # Only add seed if it's not None and not -1 (which means random)
        if seed is not None and seed != -1:
            payload["seed"] = seed

        self.logger.debug(f"API request payload: {payload}")

        # Use a separate method for the actual API call to isolate potential KeyboardInterrupt issues
        return self._execute_api_request(headers, payload, width, height)

    def _execute_api_request(self, headers, payload, width=1024, height=768):
        """Execute the API request with robust error handling.

        This method is separated from generate_image to isolate potential
        KeyboardInterrupt issues and make the code more robust.

        Args:
            headers (dict): Request headers
            payload (dict): Request payload
            width (int): Width of the image to generate
            height (int): Height of the image to generate

        Returns:
            PIL.Image.Image: Generated image as PIL Image object

        Raises:
            Exception: If the API request fails
        """
        try:
            # Make the API request
            self.logger.debug(f"Sending request to {self.BASE_URL}")
            response = requests.post(self.BASE_URL, headers=headers, json=payload)

            if response.status_code != 200:
                self.logger.error(f"API request failed with status code {response.status_code}")
                self.logger.error(f"Response content: {response.text}")

                # Try to parse the error response for more details
                try:
                    error_json = response.json()
                    error_message = error_json.get('error', {}).get('message', response.text)
                    self.logger.error(f"Error details: {error_message}")

                    # Check if it's a rate limit error (429)
                    if response.status_code == 429 and "rate limit" in error_message.lower():
                        # Try to extract the actual rate limit from the error message
                        rate_limit_info = "6 queries per minute"  # Default based on API documentation
                        if "6.0 queries per minute" in error_message:
                            rate_limit_info = "6 queries per minute"
                        elif "queries per minute" in error_message:
                            # Try to extract the actual number from the error message
                            import re
                            match = re.search(r'(\d+(?:\.\d+)?)\s+queries per minute', error_message)
                            if match:
                                rate_limit_info = f"{match.group(1)} queries per minute"

                        self.logger.warning(f"Rate limit exceeded. The FLUX.1-schnell-Free model has a limit of {rate_limit_info}.")
                        raise requests.exceptions.HTTPError(
                            f"Rate limit exceeded. Please wait a moment before trying again. The FLUX.1-schnell-Free model has a limit of {rate_limit_info}.",
                            response=response
                        )
                except json.JSONDecodeError:
                    error_message = response.text

                # Raise the exception with detailed error message
                raise requests.exceptions.HTTPError(f"API request failed with status code {response.status_code}: {error_message}", response=response)

            # Process the response
            result = response.json()
            self.logger.debug("Successfully received response from API")

            # Create a BytesIO object from the base64 data
            image_bytes = BytesIO(base64.b64decode(result["data"][0]["b64_json"]))

            # Open the image from the BytesIO object
            image = Image.open(image_bytes)

            # Make a copy to ensure the image is fully loaded and not dependent on the BytesIO object
            image_copy = image.copy()
            self.logger.debug(f"Successfully decoded image: {image_copy.width}x{image_copy.height}")

            return image_copy

        except KeyboardInterrupt:
            self.logger.error("KeyboardInterrupt detected during API request", exc_info=True)
            # Create and return a blank image instead of raising an exception
            blank_image = Image.new('RGB', (width, height), color='black')
            return blank_image

        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    error_message = error_detail.get('error', {}).get('message', str(e))
                    self.logger.error(f"API error details: {error_message}")
                    raise Exception(f"API request failed: {error_message}")
                except json.JSONDecodeError:
                    self.logger.error(f"Could not parse error response: {e.response.text}")
                    raise Exception(f"API request failed: {str(e)}")
            else:
                raise Exception(f"API request failed: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error during API request: {str(e)}", exc_info=True)
            raise Exception(f"Unexpected error: {str(e)}")

    def get_available_models(self):
        """Get list of available models.

        Returns:
            list: List of available models with id and name.
        """
        # Together AI currently only supports the free FLUX.1-schnell model in this application
        # Return a list with a single model entry
        return [
            {
                "id": "black-forest-labs/FLUX.1-schnell-Free",
                "name": "FLUX.1-schnell-Free"
            }
        ]

    def update_api_key(self, api_key):
        """Update the API key.

        Args:
            api_key (str): New API key to use.
        """
        self.logger.debug(f"Updating API key: {'*' * 5}{api_key[-5:] if api_key else 'None'}")
        self.api_key = api_key

    def get_rate_limit_wait_time(self):
        """Get the recommended wait time between requests to respect rate limits.

        Returns:
            int: Wait time in seconds between requests
        """
        # Based on Together AI's FLUX.1-schnell-Free model limit of 6 queries per minute
        # We use 12 seconds (5 requests per minute) to stay safely under the limit
        return 12

    def parse_rate_limit_from_error(self, error_message):
        """Parse rate limit information from API error message.

        Args:
            error_message (str): Error message from API

        Returns:
            tuple: (queries_per_minute, wait_time_seconds)
        """
        import re

        # Try to extract the rate limit number from the error message
        match = re.search(r'(\d+(?:\.\d+)?)\s+queries per minute', error_message)
        if match:
            queries_per_minute = float(match.group(1))
            # Calculate wait time: 60 seconds / queries_per_minute + buffer
            wait_time = int((60 / queries_per_minute) + 2)  # Add 2 second buffer
            return queries_per_minute, wait_time

        # Default fallback
        return 6.0, 12

    def is_retryable_error(self, error_message, status_code=None):
        """Check if an error should be retried.

        Args:
            error_message (str): Error message from API
            status_code (int, optional): HTTP status code

        Returns:
            bool: True if the error should be retried
        """
        # Rate limit errors should be retried
        if "rate limit" in error_message.lower():
            return True

        # Server errors (5xx) should be retried
        if status_code and 500 <= status_code < 600:
            return True

        # Specific server error messages
        server_error_keywords = [
            "internal server error",
            "bad gateway",
            "service unavailable",
            "gateway timeout",
            "server error",
            "temporarily unavailable"
        ]

        error_lower = error_message.lower()
        return any(keyword in error_lower for keyword in server_error_keywords)
